<!-- Portfolio d'Ingénieur -->

<style>
  :host {
    --primary-blue: #2563eb;
    --primary-dark: #1e40af;
    --secondary-gray: #64748b;
    --light-gray: #f8fafc;
    --dark-gray: #0f172a;
    --accent-green: #10b981;
    --white: #ffffff;

    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
      "Segoe UI Symbol";
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  /* Navigation */
  .navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
  }

  .logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    text-decoration: none;
  }

  .nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
  }

  .nav-links a {
    text-decoration: none;
    color: var(--secondary-gray);
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .nav-links a:hover {
    color: var(--primary-blue);
  }

  /* Hero Section */
  .hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
    padding: 2rem;
  }

  .hero-container {
    max-width: 1200px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
  }

  .hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.5rem;
    color: var(--primary-blue);
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .hero-description {
    font-size: 1.2rem;
    color: var(--secondary-gray);
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .hero-image {
    text-align: center;
  }

  .profile-photo {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid var(--primary-blue);
    box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
  }

  /* Buttons */
  .btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 1rem;
  }

  .btn-primary {
    background: var(--primary-blue);
    color: var(--white);
  }

  .btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.4);
  }

  .btn-secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
  }

  .btn-secondary:hover {
    background: var(--primary-blue);
    color: var(--white);
  }

  .hero-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
  }

  /* Sections générales */
  .section {
    padding: 5rem 2rem;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    text-align: center;
    margin-bottom: 3rem;
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: var(--secondary-gray);
    text-align: center;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .hero-container {
      grid-template-columns: 1fr;
      text-align: center;
      gap: 2rem;
    }

    .hero-content h1 {
      font-size: 2.5rem;
    }

    .hero-subtitle {
      font-size: 1.2rem;
    }

    .hero-description {
      font-size: 1rem;
    }

    .profile-photo {
      width: 250px;
      height: 250px;
    }

    .nav-links {
      display: none;
    }

    .hero-buttons {
      flex-direction: column;
      align-items: center;
    }

    .btn {
      width: 100%;
      max-width: 300px;
    }
  }
</style>

<!-- Navigation -->
<nav class="navbar">
  <div class="nav-container">
    <a href="#" class="logo">Portfolio</a>
    <ul class="nav-links">
      <li><a href="#accueil">Accueil</a></li>
      <li><a href="#apropos">À propos</a></li>
      <li><a href="#competences">Compétences</a></li>
      <li><a href="#projets">Projets</a></li>
      <li><a href="#experience">Expérience</a></li>
      <li><a href="#formation">Formation</a></li>
      <li><a href="#contact">Contact</a></li>
    </ul>
  </div>
</nav>

<!-- Hero Section -->
<section id="accueil" class="hero">
  <div class="hero-container">
    <div class="hero-content">
      <h1>Jean Dupont</h1>
      <div class="hero-subtitle">Ingénieur en Technologies de l'Information</div>
      <p class="hero-description">
        Passionné par le développement d'applications intelligentes et la cybersécurité.
        Mon objectif est de concevoir des solutions innovantes qui allient performance et durabilité.
      </p>
      <div class="hero-buttons">
        <a href="#projets" class="btn btn-primary">Voir mes projets</a>
        <a href="#contact" class="btn btn-secondary">Me contacter</a>
      </div>
    </div>
    <div class="hero-image">
      <img src="https://via.placeholder.com/300x300/2563eb/ffffff?text=Photo"
           alt="Photo professionnelle"
           class="profile-photo">
    </div>
  </div>
</section>

<!-- Section À propos -->
<section id="apropos" class="section" style="background: var(--white);">
  <div class="container">
    <h2 class="section-title">À propos de moi</h2>
    <p class="section-subtitle">
      Découvrez mon parcours, mes passions et mes objectifs professionnels
    </p>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: start;">
      <div>
        <h3 style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 1rem;">Mon parcours</h3>
        <p style="color: var(--secondary-gray); margin-bottom: 2rem; line-height: 1.8;">
          Diplômé en Ingénierie Informatique de l'École Nationale d'Ingénieurs,
          j'ai développé une expertise solide en développement logiciel et en cybersécurité.
          Ma passion pour l'innovation technologique m'a conduit à me spécialiser dans
          les applications intelligentes et les solutions sécurisées.
        </p>

        <h3 style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 1rem;">Mes centres d'intérêt</h3>
        <ul style="color: var(--secondary-gray); line-height: 1.8;">
          <li>Intelligence Artificielle et Machine Learning</li>
          <li>Cybersécurité et protection des données</li>
          <li>Développement d'applications web et mobile</li>
          <li>Architecture logicielle et microservices</li>
          <li>Technologies émergentes et innovation</li>
        </ul>
      </div>

      <div>
        <h3 style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 1rem;">Mes objectifs</h3>
        <p style="color: var(--secondary-gray); margin-bottom: 2rem; line-height: 1.8;">
          Mon objectif principal est de concevoir des solutions innovantes qui allient
          performance, sécurité et durabilité. Je souhaite contribuer à des projets
          qui ont un impact positif sur la société tout en repoussant les limites
          technologiques.
        </p>

        <h3 style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 1rem;">Ma philosophie</h3>
        <p style="color: var(--secondary-gray); line-height: 1.8;">
          Je crois fermement en l'apprentissage continu et en l'importance du travail
          d'équipe. Chaque projet est une opportunité d'apprendre, d'innover et de
          créer des solutions qui répondent aux besoins réels des utilisateurs.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Section Compétences -->
<section id="competences" class="section" style="background: var(--light-gray);">
  <div class="container">
    <h2 class="section-title">Compétences techniques</h2>
    <p class="section-subtitle">
      Technologies, langages et outils que je maîtrise
    </p>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
      <!-- Langages de programmation -->
      <div style="background: var(--white); padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <h3 style="font-size: 1.3rem; color: var(--primary-blue); margin-bottom: 1.5rem; display: flex; align-items: center;">
          💻 Langages de programmation
        </h3>
        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
          <span style="background: var(--primary-blue); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Java</span>
          <span style="background: var(--primary-blue); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Python</span>
          <span style="background: var(--primary-blue); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">C++</span>
          <span style="background: var(--primary-blue); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">JavaScript</span>
          <span style="background: var(--primary-blue); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">TypeScript</span>
          <span style="background: var(--primary-blue); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">SQL</span>
        </div>
      </div>

      <!-- Frameworks et Technologies -->
      <div style="background: var(--white); padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <h3 style="font-size: 1.3rem; color: var(--primary-blue); margin-bottom: 1.5rem; display: flex; align-items: center;">
          🚀 Frameworks & Technologies
        </h3>
        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
          <span style="background: var(--accent-green); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Spring Boot</span>
          <span style="background: var(--accent-green); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Angular</span>
          <span style="background: var(--accent-green); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">React</span>
          <span style="background: var(--accent-green); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">JavaFX</span>
          <span style="background: var(--accent-green); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Node.js</span>
          <span style="background: var(--accent-green); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Docker</span>
        </div>
      </div>

      <!-- Méthodologies -->
      <div style="background: var(--white); padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <h3 style="font-size: 1.3rem; color: var(--primary-blue); margin-bottom: 1.5rem; display: flex; align-items: center;">
          🧠 Méthodologies
        </h3>
        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
          <span style="background: var(--secondary-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Agile/Scrum</span>
          <span style="background: var(--secondary-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">UML</span>
          <span style="background: var(--secondary-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Design Patterns</span>
          <span style="background: var(--secondary-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">TDD</span>
          <span style="background: var(--secondary-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">DevOps</span>
        </div>
      </div>

      <!-- Outils -->
      <div style="background: var(--white); padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <h3 style="font-size: 1.3rem; color: var(--primary-blue); margin-bottom: 1.5rem; display: flex; align-items: center;">
          🔧 Outils
        </h3>
        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
          <span style="background: var(--dark-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Git</span>
          <span style="background: var(--dark-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">IntelliJ IDEA</span>
          <span style="background: var(--dark-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">VS Code</span>
          <span style="background: var(--dark-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">Figma</span>
          <span style="background: var(--dark-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">JIRA</span>
          <span style="background: var(--dark-gray); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">MySQL</span>
        </div>
      </div>
    </div>

    <!-- Soft Skills -->
    <div style="margin-top: 3rem; text-align: center;">
      <h3 style="font-size: 1.5rem; color: var(--primary-blue); margin-bottom: 2rem;">💬 Soft Skills</h3>
      <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 1rem;">
        <span style="background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark)); color: white; padding: 1rem 2rem; border-radius: 25px; font-weight: 600;">Communication</span>
        <span style="background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark)); color: white; padding: 1rem 2rem; border-radius: 25px; font-weight: 600;">Travail d'équipe</span>
        <span style="background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark)); color: white; padding: 1rem 2rem; border-radius: 25px; font-weight: 600;">Résolution de problèmes</span>
        <span style="background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark)); color: white; padding: 1rem 2rem; border-radius: 25px; font-weight: 600;">Leadership</span>
        <span style="background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark)); color: white; padding: 1rem 2rem; border-radius: 25px; font-weight: 600;">Adaptabilité</span>
      </div>
    </div>
  </div>
</section>

<router-outlet />
