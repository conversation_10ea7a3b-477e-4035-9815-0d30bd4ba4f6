<!-- Section Projets -->
<section id="projets" class="projects-section">
  <div class="container">
    <h2 class="section-title">Mes Projets</h2>
    <p class="section-subtitle">
      Découvrez quelques-uns de mes projets les plus significatifs
    </p>

    <div class="projects-grid">
      @for (project of projects; track project.title; let i = $index) {
        <div class="project-card hover-lift animate-fade-in-up" [style.animation-delay]="(i * 0.2) + 's'">
          <div class="project-image">
            <img [src]="project.image" [alt]="project.title" class="project-img">
            <div class="project-overlay">
              <div class="overlay-content">
                <span class="view-project">Voir le projet</span>
              </div>
            </div>
          </div>
          <div class="project-content">
            <h3 class="animate-fade-in-left">{{ project.title }}</h3>
            <p class="animate-fade-in-left delay-100">{{ project.description }}</p>
            <div class="project-tech">
              @for (tech of project.technologies; track tech; let j = $index) {
                <span class="tech-tag animate-slide-in"
                      [style.animation-delay]="((i * 0.2) + (j * 0.05)) + 's'">{{ tech }}</span>
              }
            </div>
            <div class="project-links animate-fade-in-up delay-300">
              <a [href]="project.github" target="_blank" class="project-link hover-lift">
                <span>🐙 GitHub</span>
              </a>
              @if (project.demo) {
                <a [href]="project.demo" target="_blank" class="project-link demo hover-lift">
                  <span>🚀 Démo</span>
                </a>
              }
            </div>
          </div>
        </div>
      }
    </div>
  </div>
</section>
