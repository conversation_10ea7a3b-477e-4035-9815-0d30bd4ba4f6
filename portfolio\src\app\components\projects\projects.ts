import { Component } from '@angular/core';

@Component({
  selector: 'app-projects',
  imports: [],
  templateUrl: './projects.html',
  styleUrl: './projects.css'
})
export class Projects {
  projects = [
    {
      title: 'Application Web Full-Stack - Gestion de Bibliothèque',
      description: 'Développement d\'une application complète de gestion de bibliothèque avec interface Angular et API Spring Boot. Gestion des livres, utilisateurs et emprunts avec base de données MySQL.',
      image: 'https://via.placeholder.com/400x250/2563eb/ffffff?text=Bibliotheque+Web',
      technologies: ['Angular', 'Spring Boot', 'MySQL', 'REST API', 'Bootstrap'],
      github: 'https://github.com/oussemahnena/bibliotheque-web',
      demo: null
    },
    {
      title: 'Site Web E-Commerce - Frontend',
      description: 'Interface utilisateur moderne pour une boutique en ligne développée avec HTML5, CSS3 et JavaScript. Design responsive avec panier d\'achat dynamique et intégration d\'APIs.',
      image: 'https://via.placeholder.com/400x250/10b981/ffffff?text=E-Commerce+Frontend',
      technologies: ['HTML5', 'CSS3', 'JavaScript', 'Bootstrap', 'REST API'],
      github: 'https://github.com/oussemahnena/ecommerce-frontend',
      demo: null
    },
    {
      title: 'API REST - Gestion des Étudiants',
      description: 'Développement d\'une API REST avec Spring Boot pour la gestion des étudiants. CRUD complet avec base de données MySQL et documentation Swagger.',
      image: 'https://via.placeholder.com/400x250/8b5cf6/ffffff?text=API+REST',
      technologies: ['Spring Boot', 'MySQL', 'Hibernate', 'Swagger', 'Postman'],
      github: 'https://github.com/oussemahnena/api-gestion-etudiants',
      demo: null
    },
    {
      title: 'Base de Données - Système de Réservation',
      description: 'Conception et implémentation d\'une base de données pour un système de réservation d\'hôtel. Modélisation complète avec requêtes SQL optimisées.',
      image: 'https://via.placeholder.com/400x250/f59e0b/ffffff?text=Base+Donnees',
      technologies: ['MySQL', 'SQL', 'Modélisation', 'phpMyAdmin', 'Diagrammes ER'],
      github: 'https://github.com/oussemahnena/bd-reservation-hotel',
      demo: null
    }
  ];
}
