import { Component } from '@angular/core';

@Component({
  selector: 'app-projects',
  imports: [],
  templateUrl: './projects.html',
  styleUrl: './projects.css'
})
export class Projects {
  projects = [
    {
      title: 'Projet Académique - Gestion Universitaire',
      description: 'Application de gestion des étudiants et cours développée dans le cadre des études à ESPRIT. Interface utilisateur intuitive avec gestion complète des données académiques.',
      image: 'https://via.placeholder.com/400x250/2563eb/ffffff?text=Gestion+Universitaire',
      technologies: ['Java', 'JavaFX', 'MySQL', 'MVC Pattern'],
      github: 'https://github.com/oussemahnena/gestion-universitaire',
      demo: null
    },
    {
      title: 'Application Web - E-Commerce',
      description: 'Projet de développement d\'une plateforme e-commerce avec Spring Boot et Angular. Implémentation des fonctionnalités CRUD, authentification et gestion des commandes.',
      image: 'https://via.placeholder.com/400x250/10b981/ffffff?text=E-Commerce',
      technologies: ['Spring Boot', 'Angular', 'MySQL', 'REST API', 'JWT'],
      github: 'https://github.com/oussemahnena/ecommerce-app',
      demo: null
    },
    {
      title: 'Portfolio Personnel',
      description: 'Développement de ce portfolio personnel avec Angular pour présenter mes compétences et projets. Design responsive et moderne.',
      image: 'https://via.placeholder.com/400x250/8b5cf6/ffffff?text=Portfolio',
      technologies: ['Angular', 'TypeScript', 'CSS3', 'Responsive Design'],
      github: 'https://github.com/oussemahnena/portfolio',
      demo: null
    },
    {
      title: 'Mini-Projet - Calculatrice Scientifique',
      description: 'Application desktop développée en Java avec interface graphique pour effectuer des calculs scientifiques avancés.',
      image: 'https://via.placeholder.com/400x250/f59e0b/ffffff?text=Calculatrice',
      technologies: ['Java', 'Swing', 'Math Libraries'],
      github: 'https://github.com/oussemahnena/calculatrice-scientifique',
      demo: null
    }
  ];
}
