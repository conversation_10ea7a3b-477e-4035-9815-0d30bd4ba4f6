import { Component } from '@angular/core';

@Component({
  selector: 'app-skills',
  imports: [],
  templateUrl: './skills.html',
  styleUrl: './skills.css'
})
export class Skills {
  skillCategories = [
    {
      title: 'Langages de programmation',
      icon: '💻',
      color: '#2563eb',
      skills: ['Java', 'C/C++', 'Python', 'JavaScript', 'TypeScript', 'SQL', 'HTML/CSS']
    },
    {
      title: 'Frameworks & Technologies Web',
      icon: '🚀',
      color: '#10b981',
      skills: ['Spring Boot', 'Angular', 'JavaFX', 'Bootstrap', 'REST API', 'JSON']
    },
    {
      title: 'Bases de Données & Outils',
      icon: '🗄️',
      color: '#8b5cf6',
      skills: ['MySQL', 'Oracle', 'MongoDB', 'JDBC', 'Hibernate', 'phpMyAdmin']
    },
    {
      title: 'Outils de Développement',
      icon: '🔧',
      color: '#0f172a',
      skills: ['IntelliJ IDEA', 'Eclipse', 'VS Code', 'Git/GitHub', 'Maven', 'Postman']
    },
    {
      title: 'Méthodologies & Concepts',
      icon: '🧠',
      color: '#64748b',
      skills: ['POO', 'UML', 'Design Patterns', 'Agile/Scrum', 'MVC', 'Architecture N-tiers']
    }
  ];

  softSkills = [
    'Apprentissage rapide',
    'Travail d\'équipe',
    'Résolution de problèmes',
    'Curiosité technique',
    'Rigueur et organisation',
    'Communication'
  ];
}
